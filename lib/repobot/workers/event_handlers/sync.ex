defmodule Repobot.Workers.EventHandlers.Sync do
  @moduledoc """
  Oban worker for handling repobot.sync events.

  This worker processes sync events that synchronize template repository files
  to their target repositories using the configured sync mode (Pull Requests or direct pushes).
  """

  use Repobot.Workers.EventHandler, max_attempts: 3

  alias Repobot.{Repositories, SourceFiles}

  @impl true
  def handle(%Events.Event{} = event) do
    payload = event.payload

    template_repository_id = payload["template_repository_id"]
    target_repository_id = payload["target_repository_id"]
    commit_sha = payload["commit_sha"]
    commit_message = payload["commit_message"]
    file_ids = payload["file_ids"] || []

    Logger.info("Processing sync event",
      event_id: event.id,
      template_repository_id: template_repository_id,
      target_repository_id: target_repository_id,
      commit_sha: commit_sha,
      files_count: length(file_ids)
    )

    with {:ok, template_repo} <- get_repository(template_repository_id),
         {:ok, target_repo} <- get_repository(target_repository_id),
         {:ok, source_files} <- get_source_files(file_ids) do
      case sync_files_to_repository(source_files, template_repo, target_repo, commit_message) do
        {:ok, result} ->
          Logger.info("Sync completed successfully",
            event_id: event.id,
            template_repository_id: template_repository_id,
            target_repository_id: target_repository_id,
            result: result
          )

          # Log the sync result
          log_sync_result(result, template_repo, target_repo, commit_sha, source_files, "success")

          {:ok, result}

        {:error, reason} ->
          Logger.error("Sync failed",
            event_id: event.id,
            template_repository_id: template_repository_id,
            target_repository_id: target_repository_id,
            error: inspect(reason)
          )

          # Log the sync failure
          log_sync_result(reason, template_repo, target_repo, commit_sha, source_files, "failed")

          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("Failed to load sync dependencies",
          event_id: event.id,
          template_repository_id: template_repository_id,
          target_repository_id: target_repository_id,
          error: inspect(reason)
        )

        {:error, reason}
    end
  end

  defp get_repository(repository_id) do
    try do
      repository = Repositories.get_repository!(repository_id)
      {:ok, repository}
    rescue
      Ecto.NoResultsError ->
        {:error, "Repository not found: #{repository_id}"}
    end
  end

  defp get_source_files(file_ids) when is_list(file_ids) and length(file_ids) > 0 do
    source_files = SourceFiles.list_source_files_by_ids(file_ids)

    if length(source_files) == length(file_ids) do
      # Preload repositories for each source file
      preloaded_files = Repobot.Repo.preload(source_files, [:repositories, :source_repository])
      {:ok, preloaded_files}
    else
      found_ids = Enum.map(source_files, & &1.id)
      missing_ids = file_ids -- found_ids
      {:error, "Source files not found: #{inspect(missing_ids)}"}
    end
  end

  defp get_source_files([]) do
    {:ok, []}
  end

  defp sync_files_to_repository(source_files, template_repo, target_repo, commit_message) do
    github_api = Application.get_env(:repobot, :github_api)

    # Create a GitHub client for the target repository
    target_client = github_api.client(target_repo.owner, target_repo.name)

    # Sync the files using the configured sync backend
    sync_backend = Application.get_env(:repobot, :sync_backend)

    sync_backend.sync_changes(source_files, template_repo, target_repo, target_client,
      commit_message: commit_message
    )
  end

  defp log_sync_result(result, template_repo, target_repo, commit_sha, source_files, status) do
    sync_event_payload = %{
      template_repository_id: template_repo.id,
      target_repository_id: target_repo.id,
      commit_sha: commit_sha,
      file_ids: Enum.map(source_files, & &1.id),
      result: if(status == "success", do: elem(result, 0), else: :error),
      details: if(status == "success", do: elem(result, 1), else: inspect(result))
    }

    Repobot.Events.log_repobot_event(
      "sync",
      sync_event_payload,
      target_repo.organization_id,
      nil,
      target_repo.id,
      status
    )
  end
end
