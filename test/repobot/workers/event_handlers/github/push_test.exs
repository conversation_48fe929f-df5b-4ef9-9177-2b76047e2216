defmodule Repobot.Workers.EventHandlers.GitHub.PushTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.{Events, Repo}
  alias Repobot.Events.Event
  alias Repobot.Workers.EventHandlers.GitHub.Push

  # Make sure mocks are set up for each test
  setup :set_mox_from_context
  setup :verify_on_exit!

  setup do
    user = create_user()
    %{user: user}
  end

  describe "handle/1" do
    test "processes push event and logs to events table", %{user: user} do
      # Create a repository
      repo =
        create_repository(%{
          template: true,
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["some/path.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: repo.id,
          status: "pending"
        })

      # Mock GitHub API for repository file refresh
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, fn :test_client, owner, repo_name, "some/path.ex" ->
        if owner == repo.owner and repo_name == repo.name do
          {:ok, "test content", %{"sha" => "abc123", "size" => 12}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify repository refresh event was logged
      refresh_events = Repo.all(from e in Event, where: e.type == "repobot.repository_refresh")
      assert length(refresh_events) > 0

      latest_refresh_event = Enum.max_by(refresh_events, & &1.inserted_at)
      assert latest_refresh_event.organization_id == repo.organization_id
      assert latest_refresh_event.payload["repository_id"] == repo.id
      assert latest_refresh_event.payload["trigger"] == "push_webhook"
    end

    test "handles push to template repository and creates sync events", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :template_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and
             path == "config/config.ex" and commit_sha == "abc123" do
          {:ok, "new content", %{"sha" => "new-sha"}}
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "new content", %{"sha" => "abc123", "size" => 11}}
        else
          {:error, "not found"}
        end
      end)

      # Count events before processing
      event_count_before = length(Repo.all(Event))

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify sync events were created
      events_after = Repo.all(Event)
      sync_events = Enum.filter(events_after, &(&1.type == "repobot.sync"))

      assert length(sync_events) > 0

      # Verify sync event details
      latest_sync_event = Enum.max_by(sync_events, & &1.inserted_at)
      assert latest_sync_event.organization_id == target_repo.organization_id
      assert latest_sync_event.payload["template_repository_id"] == template_repo.id
      assert latest_sync_event.payload["target_repository_id"] == target_repo.id
      assert latest_sync_event.payload["triggered_by_event_id"] == event.id

      # Verify source file was updated
      updated_source_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert updated_source_file.content == "new content"
    end

    test "ignores push to non-default branch", %{user: user} do
      # Create template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload for non-default branch
      payload = %{
        "ref" => "refs/heads/feature",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify no sync events were created
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) == 0
    end

    test "handles push to non-template repository", %{user: user} do
      # Create non-template repository
      repo =
        create_repository(%{
          template: false,
          name: "normal-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: repo.id,
          status: "pending"
        })

      # Mock GitHub API for repository file refresh
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, fn :test_client, owner, repo_name, "config/config.ex" ->
        if owner == repo.owner and repo_name == repo.name do
          {:ok, "test content", %{"sha" => "abc123", "size" => 12}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify no sync events were created (since it's not a template repository)
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) == 0
    end
  end
end
