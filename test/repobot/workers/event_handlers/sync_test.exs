defmodule Repobot.Workers.EventHandlers.SyncTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.{Events, Repo}
  alias Repobot.Events.Event
  alias Repobot.Workers.EventHandlers.Sync

  # Make sure mocks are set up for each test
  setup :set_mox_from_context
  setup :verify_on_exit!

  setup do
    user = create_user()
    %{user: user}
  end

  describe "handle/1" do
    test "processes sync event and synchronizes files to target repository", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "template content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with target repository
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Create sync event payload
      payload = %{
        "template_repository_id" => template_repo.id,
        "target_repository_id" => target_repo.id,
        "commit_sha" => "abc123",
        "commit_message" => "Update config",
        "file_ids" => [source_file.id],
        "source_files" => [
          %{
            "id" => source_file.id,
            "name" => source_file.name,
            "target_path" => source_file.target_path
          }
        ],
        "triggered_by_event_id" => Ecto.UUID.generate()
      }

      # Create the sync event
      {:ok, event} =
        Events.create_event(%{
          type: "repobot.sync",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: target_repo.id,
          status: "pending"
        })

      # Mock GitHub API and Sync backend
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        if owner == target_repo.owner and repo == target_repo.name do
          :target_client
        else
          raise "Unexpected client call: #{inspect({owner, repo})}"
        end
      end)

      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files, template_repo_arg, target_repo_arg, :target_client, opts ->
        # Verify arguments
        assert length(source_files) == 1
        assert List.first(source_files).id == source_file.id
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        assert opts[:commit_message] == "Update config"

        {:ok, "Files updated successfully"}
      end)

      # Perform the job
      assert :ok = perform_job(Sync, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify sync result event was logged
      sync_result_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      
      # Find the result event (not the original sync event)
      result_events = Enum.filter(sync_result_events, fn e -> 
        e.id != event.id and e.payload["template_repository_id"] == template_repo.id
      end)
      
      assert length(result_events) > 0
      
      latest_result_event = Enum.max_by(result_events, & &1.inserted_at)
      assert latest_result_event.payload["result"] == :ok
      assert latest_result_event.payload["details"] == "Files updated successfully"
      assert latest_result_event.status == "success"
    end

    test "handles sync event with multiple files", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple source files
      source_file1 =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "config content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "runtime.ex",
          target_path: "config/runtime.ex",
          content: "runtime content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source files with target repository
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file2.id
      })

      # Create sync event payload
      payload = %{
        "template_repository_id" => template_repo.id,
        "target_repository_id" => target_repo.id,
        "commit_sha" => "abc123",
        "commit_message" => "Update multiple files",
        "file_ids" => [source_file1.id, source_file2.id],
        "source_files" => [
          %{
            "id" => source_file1.id,
            "name" => source_file1.name,
            "target_path" => source_file1.target_path
          },
          %{
            "id" => source_file2.id,
            "name" => source_file2.name,
            "target_path" => source_file2.target_path
          }
        ],
        "triggered_by_event_id" => Ecto.UUID.generate()
      }

      # Create the sync event
      {:ok, event} =
        Events.create_event(%{
          type: "repobot.sync",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: target_repo.id,
          status: "pending"
        })

      # Mock GitHub API and Sync backend
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        if owner == target_repo.owner and repo == target_repo.name do
          :target_client
        else
          raise "Unexpected client call: #{inspect({owner, repo})}"
        end
      end)

      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files, template_repo_arg, target_repo_arg, :target_client, opts ->
        # Verify arguments
        assert length(source_files) == 2
        file_ids = Enum.map(source_files, & &1.id)
        assert source_file1.id in file_ids
        assert source_file2.id in file_ids
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        assert opts[:commit_message] == "Update multiple files"

        {:ok, "Multiple files updated successfully"}
      end)

      # Perform the job
      assert :ok = perform_job(Sync, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "handles sync failure and logs error", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "template content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create sync event payload
      payload = %{
        "template_repository_id" => template_repo.id,
        "target_repository_id" => target_repo.id,
        "commit_sha" => "abc123",
        "commit_message" => "Update config",
        "file_ids" => [source_file.id],
        "source_files" => [
          %{
            "id" => source_file.id,
            "name" => source_file.name,
            "target_path" => source_file.target_path
          }
        ],
        "triggered_by_event_id" => Ecto.UUID.generate()
      }

      # Create the sync event
      {:ok, event} =
        Events.create_event(%{
          type: "repobot.sync",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: target_repo.id,
          status: "pending"
        })

      # Mock GitHub API and Sync backend to fail
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        if owner == target_repo.owner and repo == target_repo.name do
          :target_client
        else
          raise "Unexpected client call: #{inspect({owner, repo})}"
        end
      end)

      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn _source_files, _template_repo, _target_repo, :target_client, _opts ->
        {:error, "GitHub API error"}
      end)

      # Perform the job
      assert {:error, "GitHub API error"} = perform_job(Sync, %{"event_id" => event.id})

      # Verify the event status was updated to failed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "failed"

      # Verify sync failure event was logged
      sync_result_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      
      # Find the result event (not the original sync event)
      result_events = Enum.filter(sync_result_events, fn e -> 
        e.id != event.id and e.payload["template_repository_id"] == template_repo.id
      end)
      
      assert length(result_events) > 0
      
      latest_result_event = Enum.max_by(result_events, & &1.inserted_at)
      assert latest_result_event.payload["result"] == :error
      assert latest_result_event.status == "failed"
    end

    test "handles missing repository error", %{user: user} do
      # Create sync event payload with non-existent repository
      non_existent_id = Ecto.UUID.generate()

      payload = %{
        "template_repository_id" => non_existent_id,
        "target_repository_id" => non_existent_id,
        "commit_sha" => "abc123",
        "commit_message" => "Update config",
        "file_ids" => [],
        "source_files" => [],
        "triggered_by_event_id" => Ecto.UUID.generate()
      }

      # Create the sync event
      {:ok, event} =
        Events.create_event(%{
          type: "repobot.sync",
          payload: payload,
          organization_id: user.default_organization_id,
          status: "pending"
        })

      # Perform the job
      assert {:error, "Repository not found: " <> _} = perform_job(Sync, %{"event_id" => event.id})

      # Verify the event status was updated to failed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "failed"
    end
  end
end
